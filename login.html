<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام التنقيط السنوي للموظفين - تسجيل الدخول</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
            text-align: right;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 3rem;
            width: 400px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .login-icon {
            font-size: 3rem;
            color: #6c5ce7;
            margin-bottom: 1rem;
        }

        .login-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #2d3436;
            margin-bottom: 0.5rem;
        }

        .login-subtitle {
            color: #636e72;
            font-size: 1rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #2d3436;
        }

        .form-label i {
            margin-left: 0.5rem;
            color: #6c5ce7;
        }

        .form-control {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            background: #ffffff !important;
            font-size: 1rem;
            font-family: 'Cairo', sans-serif;
            color: #2d3436 !important;
            transition: all 0.3s ease;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            z-index: 999;
            position: relative;
        }

        .form-control:focus {
            outline: none !important;
            border-color: #6c5ce7 !important;
            box-shadow: 0 0 0 3px rgba(108, 92, 231, 0.2) !important;
            background: #ffffff !important;
        }

        .form-control:active {
            background: #ffffff !important;
        }

        .password-container {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #636e72;
            cursor: pointer;
            font-size: 1.2rem;
        }

        .password-toggle:hover {
            color: #6c5ce7;
        }

        .btn-login {
            width: 100%;
            background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
            border: none;
            border-radius: 15px;
            padding: 1rem 2rem;
            color: white;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
            margin-top: 1rem;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(108, 92, 231, 0.3);
        }

        .btn-login:active {
            transform: translateY(0);
        }

        .error-message {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
            padding: 1rem;
            border-radius: 10px;
            margin-top: 1rem;
            text-align: center;
            display: none;
        }

        .remember-me {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .login-footer {
            text-align: center;
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
        }

        .version-info {
            color: #636e72;
            font-size: 0.9rem;
        }

        .spin {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="login-card">
        <div class="login-header">
            <i class="bi bi-shield-check login-icon"></i>
            <h2 class="login-title">نظام التنقيط السنوي</h2>
            <p class="login-subtitle">للموظفين</p>
        </div>

        <form id="loginForm">
            <div class="form-group">
                <label for="username" class="form-label">
                    <i class="bi bi-person-fill"></i>
                    اسم المستخدم
                </label>
                <input type="text" id="username" class="form-control" placeholder="أدخل اسم المستخدم" required autocomplete="username" value="" tabindex="1">
            </div>

            <div class="form-group">
                <label for="password" class="form-label">
                    <i class="bi bi-lock-fill"></i>
                    كلمة المرور
                </label>
                <div class="password-container">
                    <input type="password" id="password" class="form-control" placeholder="أدخل كلمة المرور" required autocomplete="current-password" value="" tabindex="2">
                    <button type="button" class="password-toggle" onclick="togglePassword()">
                        <i class="bi bi-eye" id="passwordIcon"></i>
                    </button>
                </div>
            </div>

            <div class="form-group">
                <div class="remember-me">
                    <input type="checkbox" id="rememberMe">
                    <label for="rememberMe">تذكرني</label>
                </div>
            </div>

            <button type="submit" class="btn-login">
                <i class="bi bi-box-arrow-in-right"></i>
                تسجيل الدخول
            </button>

            <div class="login-footer">
                <p class="version-info">الإصدار 1.0</p>
            </div>
        </form>

        <div id="errorMessage" class="error-message">
            <i class="bi bi-exclamation-triangle-fill"></i>
            <span id="errorText"></span>
        </div>
    </div></body>

    <script>
        // انتظار تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('تم تحميل الصفحة');

            // التحقق من حالة تسجيل الدخول
            const isLoggedIn = localStorage.getItem('isLoggedIn');
            if (isLoggedIn === 'true') {
                window.location.href = 'index.html';
                return;
            }

            // إعداد النموذج
            const loginForm = document.getElementById('loginForm');
            const usernameInput = document.getElementById('username');
            const passwordInput = document.getElementById('password');

            // تركيز تلقائي على حقل اسم المستخدم
            setTimeout(() => {
                usernameInput.focus();
            }, 500);

            // التأكد من أن الحقول تعمل
            usernameInput.addEventListener('focus', function() {
                console.log('تم التركيز على حقل اسم المستخدم');
            });

            passwordInput.addEventListener('focus', function() {
                console.log('تم التركيز على حقل كلمة المرور');
            });

            // معالجة إرسال النموذج
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();
                console.log('تم إرسال النموذج');

                const username = usernameInput.value.trim();
                const password = passwordInput.value.trim();

                console.log('اسم المستخدم:', username);
                console.log('كلمة المرور:', password);

                // التحقق من بيانات الدخول
                if (username === 'ISMAIL' && password === '2026') {
                    // حفظ حالة تسجيل الدخول
                    localStorage.setItem('isLoggedIn', 'true');
                    localStorage.setItem('loginTime', new Date().getTime());

                    // إضافة تأثير التحميل
                    const loginBtn = document.querySelector('.btn-login');
                    loginBtn.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> جاري تسجيل الدخول...';
                    loginBtn.disabled = true;

                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 1500);
                } else {
                    showError('اسم المستخدم أو كلمة المرور غير صحيحة');
                }
            });
        });

        // إظهار رسالة خطأ
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            const errorText = document.getElementById('errorText');

            errorText.textContent = message;
            errorDiv.style.display = 'block';

            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 3000);
        }

        // إظهار/إخفاء كلمة المرور
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const passwordIcon = document.getElementById('passwordIcon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                passwordIcon.className = 'bi bi-eye-slash';
            } else {
                passwordInput.type = 'password';
                passwordIcon.className = 'bi bi-eye';
            }
        }
    </script>
</body>
</html>
