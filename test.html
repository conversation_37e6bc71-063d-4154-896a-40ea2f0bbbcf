<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الحقول</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            direction: rtl;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 300px;
            padding: 10px;
            border: 2px solid #ccc;
            border-radius: 5px;
            font-size: 16px;
        }
        input:focus {
            border-color: #007bff;
            outline: none;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>اختبار الحقول</h1>
    
    <form id="testForm">
        <div class="form-group">
            <label for="username">اسم المستخدم:</label>
            <input type="text" id="username" placeholder="أدخل ISMAIL">
        </div>
        
        <div class="form-group">
            <label for="password">كلمة المرور:</label>
            <input type="password" id="password" placeholder="أدخل 2026">
        </div>
        
        <button type="submit">اختبار</button>
    </form>
    
    <div id="result" class="result" style="display: none;"></div>
    
    <script>
        document.getElementById('testForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const result = document.getElementById('result');
            
            result.innerHTML = `
                <h3>النتائج:</h3>
                <p><strong>اسم المستخدم:</strong> ${username}</p>
                <p><strong>كلمة المرور:</strong> ${password}</p>
                <p><strong>الحالة:</strong> ${username === 'ISMAIL' && password === '2026' ? 'صحيح ✅' : 'خطأ ❌'}</p>
            `;
            result.style.display = 'block';
            
            if (username === 'ISMAIL' && password === '2026') {
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 2000);
            }
        });
        
        // اختبار الحقول
        document.getElementById('username').addEventListener('input', function() {
            console.log('اسم المستخدم:', this.value);
        });
        
        document.getElementById('password').addEventListener('input', function() {
            console.log('كلمة المرور:', this.value);
        });
    </script>
</body>
</html>
