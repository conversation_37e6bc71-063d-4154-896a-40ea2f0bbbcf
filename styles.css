/* الخطوط العربية */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');

/* المتغيرات الأساسية */
:root {
    --primary-color: #6c5ce7;
    --secondary-color: #a29bfe;
    --accent-color: #fd79a8;
    --background-color: #f8f9fa;
    --surface-color: #ffffff;
    --text-primary: #2d3436;
    --text-secondary: #636e72;
    --shadow-light: rgba(255, 255, 255, 0.8);
    --shadow-dark: rgba(0, 0, 0, 0.15);
    --gradient-primary: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
    --gradient-secondary: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%);
    --border-radius: 20px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
    direction: rtl;
    text-align: right;
}

/* تصميم صفحة تسجيل الدخول */
.login-body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.login-container {
    position: relative;
    z-index: 10;
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: var(--border-radius);
    padding: 3rem;
    width: 400px;
    box-shadow: 
        20px 20px 60px rgba(0, 0, 0, 0.1),
        -20px -20px 60px rgba(255, 255, 255, 0.8),
        inset 5px 5px 10px rgba(0, 0, 0, 0.05),
        inset -5px -5px 10px rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: slideUp 0.8s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.logo-container {
    margin-bottom: 1rem;
}

.login-icon {
    font-size: 3rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.login-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.login-subtitle {
    color: var(--text-secondary);
    font-size: 1rem;
}

/* النماذج */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
}

.form-label i {
    margin-left: 0.5rem;
    color: var(--primary-color);
}

.neumorphic-input {
    width: 100%;
    padding: 1rem;
    border: none;
    border-radius: 15px;
    background: var(--surface-color);
    box-shadow: 
        inset 8px 8px 16px rgba(0, 0, 0, 0.1),
        inset -8px -8px 16px rgba(255, 255, 255, 0.8);
    font-size: 1rem;
    transition: var(--transition);
    font-family: 'Cairo', sans-serif;
}

.neumorphic-input:focus {
    outline: none;
    box-shadow: 
        inset 8px 8px 16px rgba(0, 0, 0, 0.15),
        inset -8px -8px 16px rgba(255, 255, 255, 0.9),
        0 0 0 3px rgba(108, 92, 231, 0.2);
}

.password-container {
    position: relative;
}

.password-toggle {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
}

.password-toggle:hover {
    color: var(--primary-color);
}

/* الأزرار */
.neumorphic-btn {
    background: var(--gradient-primary);
    border: none;
    border-radius: 15px;
    padding: 1rem 2rem;
    color: white;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 
        8px 8px 16px rgba(0, 0, 0, 0.2),
        -8px -8px 16px rgba(255, 255, 255, 0.8);
    font-family: 'Cairo', sans-serif;
}

.neumorphic-btn:hover {
    transform: translateY(-2px);
    box-shadow: 
        12px 12px 20px rgba(0, 0, 0, 0.25),
        -12px -12px 20px rgba(255, 255, 255, 0.9);
}

.neumorphic-btn:active {
    transform: translateY(0);
    box-shadow: 
        4px 4px 8px rgba(0, 0, 0, 0.2),
        -4px -4px 8px rgba(255, 255, 255, 0.8);
}

.btn-login {
    width: 100%;
    margin-top: 1rem;
}

/* رسائل الخطأ */
.error-message {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    padding: 1rem;
    border-radius: 10px;
    margin-top: 1rem;
    text-align: center;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* الأشكال المتحركة في الخلفية */
.background-animation {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.floating-shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 80px;
    height: 80px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 120px;
    height: 120px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.shape-3 {
    width: 60px;
    height: 60px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

.shape-4 {
    width: 100px;
    height: 100px;
    top: 10%;
    right: 30%;
    animation-delay: 1s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
    }
}

/* تأثيرات الدوران */
.spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* تذكرني */
.remember-me {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.neumorphic-checkbox {
    width: 20px;
    height: 20px;
    border-radius: 5px;
    background: var(--surface-color);
    box-shadow: 
        inset 4px 4px 8px rgba(0, 0, 0, 0.1),
        inset -4px -4px 8px rgba(255, 255, 255, 0.8);
    border: none;
    cursor: pointer;
}

.login-footer {
    text-align: center;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.version-info {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .login-card {
        width: 90%;
        padding: 2rem;
        margin: 1rem;
    }

    .login-title {
        font-size: 1.5rem;
    }
}

/* تصميم الواجهة الرئيسية */
.neumorphic-nav {
    background: var(--surface-color);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.1),
        0 -2px 16px rgba(255, 255, 255, 0.8);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1rem 0;
    margin-bottom: 2rem;
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.navbar-brand i {
    margin-left: 0.5rem;
}

.navbar-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.btn-nav {
    background: var(--surface-color);
    border: none;
    border-radius: 12px;
    padding: 0.75rem 1.5rem;
    color: var(--text-primary);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    box-shadow:
        6px 6px 12px rgba(0, 0, 0, 0.1),
        -6px -6px 12px rgba(255, 255, 255, 0.8);
    font-family: 'Cairo', sans-serif;
}

.btn-nav:hover {
    transform: translateY(-2px);
    box-shadow:
        8px 8px 16px rgba(0, 0, 0, 0.15),
        -8px -8px 16px rgba(255, 255, 255, 0.9);
}

.btn-nav i {
    margin-left: 0.5rem;
}

.btn-logout {
    background: var(--gradient-secondary);
    color: white;
}

.main-container {
    display: flex;
    gap: 2rem;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1rem;
}

.sidebar {
    width: 300px;
    height: fit-content;
    padding: 1.5rem;
    position: sticky;
    top: 2rem;
}

.sidebar-title {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-weight: 600;
}

.sidebar-title i {
    margin-left: 0.5rem;
    color: var(--primary-color);
}

.search-box {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.btn-search {
    background: var(--gradient-primary);
    border: none;
    border-radius: 10px;
    padding: 0.75rem;
    color: white;
    cursor: pointer;
    transition: var(--transition);
    box-shadow:
        4px 4px 8px rgba(0, 0, 0, 0.2),
        -4px -4px 8px rgba(255, 255, 255, 0.8);
}

.btn-search:hover {
    transform: translateY(-1px);
}

.employee-list {
    max-height: 400px;
    overflow-y: auto;
}

.employee-item {
    background: var(--surface-color);
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: var(--transition);
    box-shadow:
        4px 4px 8px rgba(0, 0, 0, 0.1),
        -4px -4px 8px rgba(255, 255, 255, 0.8);
}

.employee-item:hover {
    transform: translateY(-2px);
    box-shadow:
        6px 6px 12px rgba(0, 0, 0, 0.15),
        -6px -6px 12px rgba(255, 255, 255, 0.9);
}

.employee-item.active {
    background: var(--gradient-primary);
    color: white;
}

.content-area {
    flex: 1;
}

.neumorphic-card {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow:
        20px 20px 40px rgba(0, 0, 0, 0.1),
        -20px -20px 40px rgba(255, 255, 255, 0.8),
        inset 2px 2px 4px rgba(0, 0, 0, 0.05),
        inset -2px -2px 4px rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.card-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid rgba(108, 92, 231, 0.1);
}

.card-title {
    color: var(--text-primary);
    font-weight: 600;
    margin: 0;
}

.card-title i {
    margin-left: 0.5rem;
    color: var(--primary-color);
}

.employee-form .row {
    margin-bottom: 1rem;
}

.neumorphic-select {
    width: 100%;
    padding: 1rem;
    border: none;
    border-radius: 15px;
    background: var(--surface-color);
    box-shadow:
        inset 8px 8px 16px rgba(0, 0, 0, 0.1),
        inset -8px -8px 16px rgba(255, 255, 255, 0.8);
    font-size: 1rem;
    transition: var(--transition);
    font-family: 'Cairo', sans-serif;
    color: var(--text-primary);
}

.neumorphic-select:focus {
    outline: none;
    box-shadow:
        inset 8px 8px 16px rgba(0, 0, 0, 0.15),
        inset -8px -8px 16px rgba(255, 255, 255, 0.9),
        0 0 0 3px rgba(108, 92, 231, 0.2);
}

.neumorphic-textarea {
    width: 100%;
    padding: 1rem;
    border: none;
    border-radius: 15px;
    background: var(--surface-color);
    box-shadow:
        inset 8px 8px 16px rgba(0, 0, 0, 0.1),
        inset -8px -8px 16px rgba(255, 255, 255, 0.8);
    font-size: 1rem;
    transition: var(--transition);
    font-family: 'Cairo', sans-serif;
    color: var(--text-primary);
    resize: vertical;
    min-height: 100px;
}

.neumorphic-textarea:focus {
    outline: none;
    box-shadow:
        inset 8px 8px 16px rgba(0, 0, 0, 0.15),
        inset -8px -8px 16px rgba(255, 255, 255, 0.9),
        0 0 0 3px rgba(108, 92, 231, 0.2);
}
