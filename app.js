// متغيرات عامة
let employees = [];
let currentEmployeeId = null;
let isEditMode = false;

// تحميل البيانات عند بدء التطبيق
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من تسجيل الدخول
    checkAuthentication();
    
    // تحميل البيانات المحفوظة
    loadEmployees();
    
    // إعداد سنوات التقييم
    setupEvaluationYears();
    
    // إعداد النموذج
    setupForm();
    
    // تحديث قائمة الموظفين
    updateEmployeeList();
});

// التحقق من تسجيل الدخول
function checkAuthentication() {
    const isLoggedIn = localStorage.getItem('isLoggedIn');
    if (!isLoggedIn || isLoggedIn !== 'true') {
        window.location.href = 'login.html';
        return;
    }
}

// تسجيل الخروج
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('isLoggedIn');
        localStorage.removeItem('loginTime');
        window.location.href = 'login.html';
    }
}

// تحميل بيانات الموظفين
function loadEmployees() {
    const savedEmployees = localStorage.getItem('employees');
    if (savedEmployees) {
        employees = JSON.parse(savedEmployees);
    }
}

// حفظ بيانات الموظفين
function saveEmployees() {
    localStorage.setItem('employees', JSON.stringify(employees));
    
    // تصدير إلى CSV تلقائياً
    exportToCSV();
}

// إعداد سنوات التقييم
function setupEvaluationYears() {
    const yearSelect = document.getElementById('evaluationYear');
    const currentYear = new Date().getFullYear();
    
    // إضافة السنوات من 2024 إلى السنة الحالية + 10 سنوات
    for (let year = 2024; year <= currentYear + 10; year++) {
        const option = document.createElement('option');
        option.value = year;
        option.textContent = year;
        if (year === currentYear) {
            option.selected = true;
        }
        yearSelect.appendChild(option);
    }
}

// إعداد النموذج
function setupForm() {
    const form = document.getElementById('employeeForm');
    form.addEventListener('submit', handleFormSubmit);
}

// معالجة إرسال النموذج
function handleFormSubmit(e) {
    e.preventDefault();
    
    const formData = getFormData();
    
    if (validateFormData(formData)) {
        if (isEditMode && currentEmployeeId) {
            updateEmployee(formData);
        } else {
            addEmployee(formData);
        }
    }
}

// الحصول على بيانات النموذج
function getFormData() {
    return {
        id: currentEmployeeId || generateId(),
        firstName: document.getElementById('firstName').value.trim(),
        lastName: document.getElementById('lastName').value.trim(),
        rank: document.getElementById('rank').value,
        idNumber: document.getElementById('idNumber').value.trim(),
        workplace: document.getElementById('workplace').value.trim(),
        battalion: document.getElementById('battalion').value,
        profession: document.getElementById('profession').value.trim(),
        birthDate: document.getElementById('birthDate').value,
        age: parseInt(document.getElementById('age').value),
        maritalStatus: document.getElementById('maritalStatus').value,
        children: parseInt(document.getElementById('children').value) || 0,
        evaluationYear: parseInt(document.getElementById('evaluationYear').value),
        score: parseInt(document.getElementById('score').value),
        comment: document.getElementById('comment').value,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    };
}

// التحقق من صحة البيانات
function validateFormData(data) {
    const requiredFields = ['firstName', 'lastName', 'rank', 'idNumber', 'workplace', 'battalion', 'profession', 'birthDate', 'maritalStatus', 'evaluationYear', 'score'];
    
    for (let field of requiredFields) {
        if (!data[field] || data[field] === '') {
            showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
            return false;
        }
    }
    
    // التحقق من عدم تكرار رقم الذاتية
    const existingEmployee = employees.find(emp => 
        emp.idNumber === data.idNumber && emp.id !== data.id
    );
    
    if (existingEmployee) {
        showAlert('رقم الذاتية موجود مسبقاً', 'error');
        return false;
    }
    
    return true;
}

// إضافة موظف جديد
function addEmployee(data) {
    employees.push(data);
    saveEmployees();
    updateEmployeeList();
    clearForm();
    showAlert('تم حفظ بيانات الموظف بنجاح', 'success');
}

// تحديث بيانات موظف
function updateEmployee(data) {
    const index = employees.findIndex(emp => emp.id === currentEmployeeId);
    if (index !== -1) {
        data.createdAt = employees[index].createdAt; // الاحتفاظ بتاريخ الإنشاء الأصلي
        employees[index] = data;
        saveEmployees();
        updateEmployeeList();
        clearForm();
        showAlert('تم تحديث بيانات الموظف بنجاح', 'success');
    }
}

// حساب العمر تلقائياً
function calculateAge() {
    const birthDate = document.getElementById('birthDate').value;
    if (birthDate) {
        const today = new Date();
        const birth = new Date(birthDate);
        let age = today.getFullYear() - birth.getFullYear();
        const monthDiff = today.getMonth() - birth.getMonth();
        
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
            age--;
        }
        
        document.getElementById('age').value = age;
    }
}

// توليد الملاحظة تلقائياً
function generateComment() {
    const score = parseInt(document.getElementById('score').value);
    const year = document.getElementById('evaluationYear').value;
    const firstName = document.getElementById('firstName').value;
    const lastName = document.getElementById('lastName').value;
    
    if (!score || !year) return;
    
    const comments = {
        4: [
            `يظهر ${firstName} ${lastName} أداءً مقبولاً في عمله خلال سنة ${year}، مع ضرورة تحسين مستوى الانضباط والالتزام بالمواعيد المحددة.`,
            `المردود المهني للموظف ${firstName} ${lastName} في حاجة إلى تطوير، ويُنصح بالمشاركة في دورات تدريبية لتعزيز المهارات المطلوبة.`,
            `يحتاج ${firstName} ${lastName} إلى مزيد من الجهد لتحقيق المعايير المطلوبة، مع التركيز على تحسين جودة العمل المنجز.`,
            `الأداء العام للموظف مقبول لكن يتطلب المزيد من الاهتمام بالتفاصيل والدقة في تنفيذ المهام الموكلة إليه.`
        ],
        5: [
            `يُظهر ${firstName} ${lastName} أداءً جيداً في معظم جوانب عمله لسنة ${year}، مع الحفاظ على مستوى مقبول من الانضباط والالتزام.`,
            `المردود المهني للموظف ${firstName} ${lastName} جيد ويساهم بشكل إيجابي في تحقيق أهداف الوحدة، مع إمكانية التطوير أكثر.`,
            `يتمتع ${firstName} ${lastName} بمهارات مهنية جيدة ويؤدي مهامه بكفاءة مناسبة، مع ضرورة الاستمرار في التطوير الذاتي.`,
            `الأداء العام للموظف جيد ويُظهر التزاماً بالقوانين واللوائح، مع القدرة على تحمل المسؤوليات المناطة به.`
        ],
        6: [
            `يتميز ${firstName} ${lastName} بأداء ممتاز خلال سنة ${year}، مع إظهار مستوى عالٍ من الانضباط والالتزام بالواجبات المهنية.`,
            `المردود المهني للموظف ${firstName} ${lastName} ممتاز ويساهم بفعالية في تطوير العمل وتحقيق النتائج المرجوة بكفاءة عالية.`,
            `يُظهر ${firstName} ${lastName} قدرات قيادية واضحة ومهارات متقدمة في التعامل مع المواقف المختلفة بحكمة ومهنية.`,
            `الأداء العام للموظف ممتاز ويُعتبر قدوة للزملاء، مع إظهار روح المبادرة والإبداع في حل المشكلات.`
        ],
        7: [
            `يحقق ${firstName} ${lastName} أداءً استثنائياً ومتميزاً جداً خلال سنة ${year}، مع إظهار أعلى مستويات الانضباط والتفاني في العمل.`,
            `المردود المهني للموظف ${firstName} ${lastName} استثنائي ويتجاوز التوقعات، مع تقديم إنجازات نوعية تساهم في تطوير المؤسسة.`,
            `يتمتع ${firstName} ${lastName} بمهارات قيادية استثنائية وقدرة فائقة على الإبداع والابتكار في جميع جوانب العمل المكلف به.`,
            `الأداء العام للموظف استثنائي ومثالي، ويُعتبر نموذجاً يُحتذى به في الالتزام والتميز والعطاء المتواصل.`
        ]
    };
    
    const scoreComments = comments[score];
    if (scoreComments) {
        // اختيار تعليق عشوائي أو بناءً على السنة لضمان التنوع
        const commentIndex = (parseInt(year) + score) % scoreComments.length;
        document.getElementById('comment').value = scoreComments[commentIndex];
    }
}

// توليد معرف فريد
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// تحديث قائمة الموظفين
function updateEmployeeList() {
    const listContainer = document.getElementById('employeeList');
    listContainer.innerHTML = '';
    
    if (employees.length === 0) {
        listContainer.innerHTML = '<p class="text-center text-muted">لا توجد بيانات موظفين</p>';
        return;
    }
    
    employees.forEach(employee => {
        const item = document.createElement('div');
        item.className = 'employee-item';
        item.innerHTML = `
            <div class="employee-name">${employee.firstName} ${employee.lastName}</div>
            <div class="employee-details">
                <small>${employee.rank} - ${employee.idNumber}</small><br>
                <small>${employee.battalion}</small>
            </div>
        `;
        
        item.addEventListener('click', () => selectEmployee(employee.id));
        listContainer.appendChild(item);
    });
}

// اختيار موظف من القائمة
function selectEmployee(employeeId) {
    const employee = employees.find(emp => emp.id === employeeId);
    if (employee) {
        fillForm(employee);
        currentEmployeeId = employeeId;
        
        // تحديث التحديد في القائمة
        document.querySelectorAll('.employee-item').forEach(item => {
            item.classList.remove('active');
        });
        event.target.closest('.employee-item').classList.add('active');
    }
}

// ملء النموذج ببيانات الموظف
function fillForm(employee) {
    document.getElementById('firstName').value = employee.firstName;
    document.getElementById('lastName').value = employee.lastName;
    document.getElementById('rank').value = employee.rank;
    document.getElementById('idNumber').value = employee.idNumber;
    document.getElementById('workplace').value = employee.workplace;
    document.getElementById('battalion').value = employee.battalion;
    document.getElementById('profession').value = employee.profession;
    document.getElementById('birthDate').value = employee.birthDate;
    document.getElementById('age').value = employee.age;
    document.getElementById('maritalStatus').value = employee.maritalStatus;
    document.getElementById('children').value = employee.children;
    document.getElementById('evaluationYear').value = employee.evaluationYear;
    document.getElementById('score').value = employee.score;
    document.getElementById('comment').value = employee.comment;
}

// مسح النموذج
function clearForm() {
    document.getElementById('employeeForm').reset();
    document.getElementById('age').value = '';
    document.getElementById('comment').value = '';
    currentEmployeeId = null;
    isEditMode = false;
    
    // إزالة التحديد من القائمة
    document.querySelectorAll('.employee-item').forEach(item => {
        item.classList.remove('active');
    });
    
    // تعيين السنة الحالية كافتراضية
    const currentYear = new Date().getFullYear();
    document.getElementById('evaluationYear').value = currentYear;
}

// تفعيل وضع التعديل
function editEmployee() {
    if (!currentEmployeeId) {
        showAlert('يرجى اختيار موظف للتعديل', 'error');
        return;
    }
    isEditMode = true;
    showAlert('تم تفعيل وضع التعديل', 'info');
}

// حذف موظف
function deleteEmployee() {
    if (!currentEmployeeId) {
        showAlert('يرجى اختيار موظف للحذف', 'error');
        return;
    }
    
    if (confirm('هل أنت متأكد من حذف بيانات هذا الموظف؟')) {
        const index = employees.findIndex(emp => emp.id === currentEmployeeId);
        if (index !== -1) {
            employees.splice(index, 1);
            saveEmployees();
            updateEmployeeList();
            clearForm();
            showAlert('تم حذف بيانات الموظف بنجاح', 'success');
        }
    }
}

// البحث عن موظف
function searchEmployee() {
    const searchTerm = document.getElementById('employeeSearch').value.trim();
    if (!searchTerm) {
        updateEmployeeList();
        return;
    }
    
    const filteredEmployees = employees.filter(emp => 
        emp.idNumber.includes(searchTerm) ||
        emp.firstName.includes(searchTerm) ||
        emp.lastName.includes(searchTerm)
    );
    
    const listContainer = document.getElementById('employeeList');
    listContainer.innerHTML = '';
    
    if (filteredEmployees.length === 0) {
        listContainer.innerHTML = '<p class="text-center text-muted">لم يتم العثور على نتائج</p>';
        return;
    }
    
    filteredEmployees.forEach(employee => {
        const item = document.createElement('div');
        item.className = 'employee-item';
        item.innerHTML = `
            <div class="employee-name">${employee.firstName} ${employee.lastName}</div>
            <div class="employee-details">
                <small>${employee.rank} - ${employee.idNumber}</small><br>
                <small>${employee.battalion}</small>
            </div>
        `;
        
        item.addEventListener('click', () => selectEmployee(employee.id));
        listContainer.appendChild(item);
    });
}

// إغلاق الواجهة
function closeInterface() {
    if (confirm('هل أنت متأكد من إغلاق الواجهة؟')) {
        window.close();
    }
}

// عرض رسالة تنبيه
function showAlert(message, type = 'info') {
    // إنشاء عنصر التنبيه
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.innerHTML = `
        <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'}"></i>
        ${message}
    `;

    // إضافة التنبيه إلى الصفحة
    document.body.appendChild(alert);

    // تحديد موقع التنبيه
    alert.style.position = 'fixed';
    alert.style.top = '20px';
    alert.style.right = '20px';
    alert.style.zIndex = '9999';
    alert.style.minWidth = '300px';

    // إزالة التنبيه بعد 3 ثوانٍ
    setTimeout(() => {
        alert.remove();
    }, 3000);
}

// عرض الإحصائيات
function showStatistics() {
    const modal = document.getElementById('statisticsModal');
    const content = document.getElementById('statisticsContent');

    // حساب الإحصائيات
    const stats = calculateStatistics();

    content.innerHTML = `
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="bi bi-people-fill"></i>
                </div>
                <div class="stat-number">${stats.totalEmployees}</div>
                <div class="stat-label">إجمالي الموظفين</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <i class="bi bi-star-fill"></i>
                </div>
                <div class="stat-number">${stats.averageScore.toFixed(1)}</div>
                <div class="stat-label">متوسط النقاط</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <i class="bi bi-trophy-fill"></i>
                </div>
                <div class="stat-number">${stats.excellentEmployees}</div>
                <div class="stat-label">موظفين متميزين (7 نقاط)</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <i class="bi bi-calendar-check"></i>
                </div>
                <div class="stat-number">${stats.currentYearEvaluations}</div>
                <div class="stat-label">تقييمات السنة الحالية</div>
            </div>
        </div>

        <div class="score-chart">
            <h5 class="chart-title">توزيع النقاط</h5>
            <div class="chart-bars">
                <div class="chart-bar" data-score="4 نقاط" style="height: ${(stats.scoreDistribution[4] / stats.totalEmployees) * 100}%">
                    ${stats.scoreDistribution[4]}
                </div>
                <div class="chart-bar" data-score="5 نقاط" style="height: ${(stats.scoreDistribution[5] / stats.totalEmployees) * 100}%">
                    ${stats.scoreDistribution[5]}
                </div>
                <div class="chart-bar" data-score="6 نقاط" style="height: ${(stats.scoreDistribution[6] / stats.totalEmployees) * 100}%">
                    ${stats.scoreDistribution[6]}
                </div>
                <div class="chart-bar" data-score="7 نقاط" style="height: ${(stats.scoreDistribution[7] / stats.totalEmployees) * 100}%">
                    ${stats.scoreDistribution[7]}
                </div>
            </div>
        </div>

        <div class="stats-grid">
            ${Object.entries(stats.battalionStats).map(([battalion, count]) => `
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="bi bi-building"></i>
                    </div>
                    <div class="stat-number">${count}</div>
                    <div class="stat-label">${battalion}</div>
                </div>
            `).join('')}
        </div>
    `;

    modal.style.display = 'flex';
}

// حساب الإحصائيات
function calculateStatistics() {
    const currentYear = new Date().getFullYear();

    const stats = {
        totalEmployees: employees.length,
        averageScore: 0,
        excellentEmployees: 0,
        currentYearEvaluations: 0,
        scoreDistribution: { 4: 0, 5: 0, 6: 0, 7: 0 },
        battalionStats: {}
    };

    if (employees.length === 0) return stats;

    let totalScore = 0;

    employees.forEach(emp => {
        totalScore += emp.score;

        if (emp.score === 7) {
            stats.excellentEmployees++;
        }

        if (emp.evaluationYear === currentYear) {
            stats.currentYearEvaluations++;
        }

        stats.scoreDistribution[emp.score]++;

        if (stats.battalionStats[emp.battalion]) {
            stats.battalionStats[emp.battalion]++;
        } else {
            stats.battalionStats[emp.battalion] = 1;
        }
    });

    stats.averageScore = totalScore / employees.length;

    return stats;
}

// عرض البيانات في جدول
function showDataView() {
    const modal = document.getElementById('dataViewModal');
    const content = document.getElementById('dataViewContent');

    if (employees.length === 0) {
        content.innerHTML = '<p class="text-center">لا توجد بيانات لعرضها</p>';
    } else {
        content.innerHTML = `
            <div class="table-responsive">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>الاسم الكامل</th>
                            <th>الرتبة</th>
                            <th>رقم الذاتية</th>
                            <th>الكتيبة</th>
                            <th>السن</th>
                            <th>سنة التقييم</th>
                            <th>النقطة</th>
                            <th>الحالة العائلية</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${employees.map(emp => `
                            <tr>
                                <td>${emp.firstName} ${emp.lastName}</td>
                                <td>${emp.rank}</td>
                                <td>${emp.idNumber}</td>
                                <td>${emp.battalion}</td>
                                <td>${emp.age}</td>
                                <td>${emp.evaluationYear}</td>
                                <td>${emp.score}</td>
                                <td>${emp.maritalStatus}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    modal.style.display = 'flex';
}

// إغلاق النافذة المنبثقة
function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

// تصدير البيانات إلى CSV
function exportData() {
    if (employees.length === 0) {
        showAlert('لا توجد بيانات للتصدير', 'error');
        return;
    }

    exportToCSV();
    showAlert('تم تصدير البيانات بنجاح', 'success');
}

// تصدير إلى CSV
function exportToCSV() {
    const headers = [
        'الاسم',
        'اللقب',
        'الرتبة',
        'رقم الذاتية',
        'مكان العمل',
        'الكتيبة',
        'الصفة المهنية',
        'تاريخ الميلاد',
        'السن',
        'الحالة العائلية',
        'عدد الأولاد',
        'سنة التقييم',
        'النقطة',
        'الملاحظة'
    ];

    const csvContent = [
        headers.join(','),
        ...employees.map(emp => [
            emp.firstName,
            emp.lastName,
            emp.rank,
            emp.idNumber,
            emp.workplace,
            emp.battalion,
            emp.profession,
            emp.birthDate,
            emp.age,
            emp.maritalStatus,
            emp.children,
            emp.evaluationYear,
            emp.score,
            `"${emp.comment.replace(/"/g, '""')}"`
        ].join(','))
    ].join('\n');

    // إضافة BOM للدعم العربي
    const BOM = '\uFEFF';
    const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });

    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `تقييم_الموظفين_${new Date().getFullYear()}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// استيراد البيانات
function importData() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.csv';
    input.onchange = handleFileImport;
    input.click();
}

// معالجة استيراد الملف
function handleFileImport(event) {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const csv = e.target.result;
            const lines = csv.split('\n');
            const headers = lines[0].split(',');

            const importedEmployees = [];

            for (let i = 1; i < lines.length; i++) {
                if (lines[i].trim() === '') continue;

                const values = parseCSVLine(lines[i]);
                if (values.length >= 14) {
                    const employee = {
                        id: generateId(),
                        firstName: values[0],
                        lastName: values[1],
                        rank: values[2],
                        idNumber: values[3],
                        workplace: values[4],
                        battalion: values[5],
                        profession: values[6],
                        birthDate: values[7],
                        age: parseInt(values[8]),
                        maritalStatus: values[9],
                        children: parseInt(values[10]) || 0,
                        evaluationYear: parseInt(values[11]),
                        score: parseInt(values[12]),
                        comment: values[13].replace(/^"|"$/g, '').replace(/""/g, '"'),
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString()
                    };

                    importedEmployees.push(employee);
                }
            }

            if (importedEmployees.length > 0) {
                employees = [...employees, ...importedEmployees];
                saveEmployees();
                updateEmployeeList();
                showAlert(`تم استيراد ${importedEmployees.length} موظف بنجاح`, 'success');
            } else {
                showAlert('لم يتم العثور على بيانات صالحة في الملف', 'error');
            }

        } catch (error) {
            showAlert('حدث خطأ أثناء استيراد الملف', 'error');
            console.error('Import error:', error);
        }
    };

    reader.readAsText(file, 'UTF-8');
}

// تحليل سطر CSV
function parseCSVLine(line) {
    const result = [];
    let current = '';
    let inQuotes = false;

    for (let i = 0; i < line.length; i++) {
        const char = line[i];

        if (char === '"') {
            if (inQuotes && line[i + 1] === '"') {
                current += '"';
                i++;
            } else {
                inQuotes = !inQuotes;
            }
        } else if (char === ',' && !inQuotes) {
            result.push(current);
            current = '';
        } else {
            current += char;
        }
    }

    result.push(current);
    return result;
}

// إضافة مستمع للبحث المباشر
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('employeeSearch');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            if (this.value.trim() === '') {
                updateEmployeeList();
            } else {
                searchEmployee();
            }
        });

        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchEmployee();
            }
        });
    }
});

// إضافة اختصارات لوحة المفاتيح
document.addEventListener('keydown', function(e) {
    // Ctrl + S للحفظ
    if (e.ctrlKey && e.key === 's') {
        e.preventDefault();
        document.getElementById('employeeForm').dispatchEvent(new Event('submit'));
    }

    // Ctrl + N لنموذج جديد
    if (e.ctrlKey && e.key === 'n') {
        e.preventDefault();
        clearForm();
    }

    // Ctrl + E للتعديل
    if (e.ctrlKey && e.key === 'e') {
        e.preventDefault();
        editEmployee();
    }

    // Delete للحذف
    if (e.key === 'Delete' && currentEmployeeId) {
        deleteEmployee();
    }

    // Escape لإغلاق النوافذ المنبثقة
    if (e.key === 'Escape') {
        closeModal('statisticsModal');
        closeModal('dataViewModal');
    }
});

// التحقق من انتهاء صلاحية الجلسة
setInterval(function() {
    const loginTime = localStorage.getItem('loginTime');
    if (loginTime) {
        const currentTime = new Date().getTime();
        const sessionDuration = 8 * 60 * 60 * 1000; // 8 ساعات

        if (currentTime - parseInt(loginTime) > sessionDuration) {
            showAlert('انتهت صلاحية الجلسة، سيتم تسجيل الخروج تلقائياً', 'error');
            setTimeout(() => {
                logout();
            }, 3000);
        }
    }
}, 60000); // فحص كل دقيقة

// حفظ تلقائي كل 5 دقائق
setInterval(function() {
    if (employees.length > 0) {
        saveEmployees();
        console.log('تم الحفظ التلقائي:', new Date().toLocaleString('ar-SA'));
    }
}, 5 * 60 * 1000);

// إضافة تأثيرات صوتية (اختيارية)
function playNotificationSound(type = 'success') {
    // يمكن إضافة أصوات تنبيه هنا إذا رغبت
    // const audio = new Audio(`sounds/${type}.mp3`);
    // audio.play().catch(() => {}); // تجاهل الأخطاء إذا لم تكن الأصوات متاحة
}

// تحسين أداء البحث
let searchTimeout;
function debounceSearch() {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
        searchEmployee();
    }, 300);
}

// إضافة إحصائيات متقدمة
function getAdvancedStatistics() {
    const currentYear = new Date().getFullYear();
    const stats = calculateStatistics();

    // إحصائيات إضافية
    const ageGroups = {
        '20-30': 0,
        '31-40': 0,
        '41-50': 0,
        '51+': 0
    };

    const maritalStatusStats = {};
    const rankStats = {};

    employees.forEach(emp => {
        // تجميع الأعمار
        if (emp.age <= 30) ageGroups['20-30']++;
        else if (emp.age <= 40) ageGroups['31-40']++;
        else if (emp.age <= 50) ageGroups['41-50']++;
        else ageGroups['51+']++;

        // إحصائيات الحالة العائلية
        maritalStatusStats[emp.maritalStatus] = (maritalStatusStats[emp.maritalStatus] || 0) + 1;

        // إحصائيات الرتب
        rankStats[emp.rank] = (rankStats[emp.rank] || 0) + 1;
    });

    return {
        ...stats,
        ageGroups,
        maritalStatusStats,
        rankStats
    };
}

// تصدير تقرير مفصل
function exportDetailedReport() {
    const stats = getAdvancedStatistics();
    const currentDate = new Date().toLocaleDateString('ar-SA');

    const reportContent = `
تقرير التنقيط السنوي للموظفين
تاريخ التقرير: ${currentDate}

الإحصائيات العامة:
- إجمالي الموظفين: ${stats.totalEmployees}
- متوسط النقاط: ${stats.averageScore.toFixed(2)}
- الموظفين المتميزين (7 نقاط): ${stats.excellentEmployees}

توزيع النقاط:
- 4 نقاط: ${stats.scoreDistribution[4]} موظف
- 5 نقاط: ${stats.scoreDistribution[5]} موظف
- 6 نقاط: ${stats.scoreDistribution[6]} موظف
- 7 نقاط: ${stats.scoreDistribution[7]} موظف

توزيع الأعمار:
- 20-30 سنة: ${stats.ageGroups['20-30']} موظف
- 31-40 سنة: ${stats.ageGroups['31-40']} موظف
- 41-50 سنة: ${stats.ageGroups['41-50']} موظف
- 51+ سنة: ${stats.ageGroups['51+']} موظف

توزيع الكتائب:
${Object.entries(stats.battalionStats).map(([battalion, count]) => `- ${battalion}: ${count} موظف`).join('\n')}

توزيع الرتب:
${Object.entries(stats.rankStats).map(([rank, count]) => `- ${rank}: ${count} موظف`).join('\n')}

توزيع الحالة العائلية:
${Object.entries(stats.maritalStatusStats).map(([status, count]) => `- ${status}: ${count} موظف`).join('\n')}
    `;

    const blob = new Blob([reportContent], { type: 'text/plain;charset=utf-8' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `تقرير_مفصل_${new Date().getFullYear()}.txt`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}
