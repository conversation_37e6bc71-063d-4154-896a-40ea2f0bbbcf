<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام التنقيط السنوي للموظفين - تسجيل الدخول</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>
<body class="login-body">
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="logo-container">
                    <i class="bi bi-shield-check login-icon"></i>
                </div>
                <h2 class="login-title">نظام التنقيط السنوي</h2>
                <p class="login-subtitle">للموظفين</p>
            </div>
            
            <form id="loginForm" class="login-form">
                <div class="form-group">
                    <label for="username" class="form-label">
                        <i class="bi bi-person-fill"></i>
                        اسم المستخدم
                    </label>
                    <input type="text" id="username" class="form-control neumorphic-input" placeholder="أدخل اسم المستخدم" required>
                </div>
                
                <div class="form-group">
                    <label for="password" class="form-label">
                        <i class="bi bi-lock-fill"></i>
                        كلمة المرور
                    </label>
                    <div class="password-container">
                        <input type="password" id="password" class="form-control neumorphic-input" placeholder="أدخل كلمة المرور" required>
                        <button type="button" class="password-toggle" onclick="togglePassword()">
                            <i class="bi bi-eye" id="passwordIcon"></i>
                        </button>
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="remember-me">
                        <input type="checkbox" id="rememberMe" class="neumorphic-checkbox">
                        <label for="rememberMe">تذكرني</label>
                    </div>
                </div>
                
                <button type="submit" class="btn-login neumorphic-btn">
                    <i class="bi bi-box-arrow-in-right"></i>
                    تسجيل الدخول
                </button>
                
                <div class="login-footer">
                    <p class="version-info">الإصدار 1.0</p>
                </div>
            </form>
            
            <div id="errorMessage" class="error-message" style="display: none;">
                <i class="bi bi-exclamation-triangle-fill"></i>
                <span id="errorText"></span>
            </div>
        </div>
        
        <div class="background-animation">
            <div class="floating-shape shape-1"></div>
            <div class="floating-shape shape-2"></div>
            <div class="floating-shape shape-3"></div>
            <div class="floating-shape shape-4"></div>
        </div>
    </div>

    <script>
        // تسجيل الدخول
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            // التحقق من بيانات الدخول
            if (username === 'ISMAIL' && password === '2026') {
                // حفظ حالة تسجيل الدخول
                localStorage.setItem('isLoggedIn', 'true');
                localStorage.setItem('loginTime', new Date().getTime());
                
                // إضافة تأثير التحميل
                const loginBtn = document.querySelector('.btn-login');
                loginBtn.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> جاري تسجيل الدخول...';
                loginBtn.disabled = true;
                
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 1500);
            } else {
                showError('اسم المستخدم أو كلمة المرور غير صحيحة');
            }
        });
        
        // إظهار رسالة خطأ
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            const errorText = document.getElementById('errorText');
            
            errorText.textContent = message;
            errorDiv.style.display = 'block';
            
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 3000);
        }
        
        // إظهار/إخفاء كلمة المرور
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const passwordIcon = document.getElementById('passwordIcon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                passwordIcon.className = 'bi bi-eye-slash';
            } else {
                passwordInput.type = 'password';
                passwordIcon.className = 'bi bi-eye';
            }
        }
        
        // التحقق من حالة تسجيل الدخول
        window.addEventListener('load', function() {
            const isLoggedIn = localStorage.getItem('isLoggedIn');
            if (isLoggedIn === 'true') {
                window.location.href = 'index.html';
            }
        });
    </script>
</body>
</html>
