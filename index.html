<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام التنقيط السنوي للموظفين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="main-styles.css">
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar neumorphic-nav">
        <div class="container-fluid">
            <div class="navbar-brand">
                <i class="bi bi-clipboard-data"></i>
                نظام التنقيط السنوي للموظفين
            </div>
            <div class="navbar-actions">
                <button class="btn-nav" onclick="showStatistics()">
                    <i class="bi bi-graph-up"></i>
                    الإحصائيات
                </button>
                <button class="btn-nav" onclick="showDataView()">
                    <i class="bi bi-table"></i>
                    عرض البيانات
                </button>
                <button class="btn-nav" onclick="exportData()">
                    <i class="bi bi-download"></i>
                    تصدير
                </button>
                <button class="btn-nav" onclick="importData()">
                    <i class="bi bi-upload"></i>
                    استيراد
                </button>
                <button class="btn-nav btn-logout" onclick="logout()">
                    <i class="bi bi-box-arrow-right"></i>
                    خروج
                </button>
            </div>
        </div>
    </nav>

    <div class="main-container">
        <!-- الشريط الجانبي -->
        <div class="sidebar neumorphic-card">
            <h5 class="sidebar-title">
                <i class="bi bi-people-fill"></i>
                قائمة الموظفين
            </h5>
            <div class="search-box">
                <input type="text" id="employeeSearch" class="neumorphic-input" placeholder="البحث برقم الذاتية...">
                <button onclick="searchEmployee()" class="btn-search">
                    <i class="bi bi-search"></i>
                </button>
            </div>
            <div id="employeeList" class="employee-list">
                <!-- قائمة الموظفين ستظهر هنا -->
            </div>
        </div>

        <!-- منطقة المحتوى الرئيسي -->
        <div class="content-area">
            <!-- نموذج إدخال البيانات -->
            <div id="dataEntryForm" class="neumorphic-card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="bi bi-person-plus-fill"></i>
                        إدخال بيانات الموظف
                    </h4>
                </div>
                
                <form id="employeeForm" class="employee-form">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">الاسم</label>
                                <input type="text" id="firstName" class="neumorphic-input" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">اللقب</label>
                                <input type="text" id="lastName" class="neumorphic-input" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">الرتبة</label>
                                <select id="rank" class="neumorphic-select" required>
                                    <option value="">اختر الرتبة</option>
                                    <option value="عميد ش">عميد ش</option>
                                    <option value="محا ش">محا ش</option>
                                    <option value="ض ش ر">ض ش ر</option>
                                    <option value="ض ش">ض ش</option>
                                    <option value="ح أ ش">ح أ ش</option>
                                    <option value="ح ش">ح ش</option>
                                    <option value="ع ش">ع ش</option>
                                    <option value="ع شبهي">ع شبهي</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">رقم الذاتية</label>
                                <input type="text" id="idNumber" class="neumorphic-input" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">مكان العمل</label>
                                <input type="text" id="workplace" class="neumorphic-input" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">الكتيبة</label>
                                <select id="battalion" class="neumorphic-select" required>
                                    <option value="">اختر الكتيبة</option>
                                    <option value="هياكل الدعم الإداري">هياكل الدعم الإداري</option>
                                    <option value="كتيبة الحماية و الأمن">كتيبة الحماية و الأمن</option>
                                    <option value="كتيبة حركة المرور و أمن الطرقات">كتيبة حركة المرور و أمن الطرقات</option>
                                    <option value="كتيبة الطريق العام">كتيبة الطريق العام</option>
                                    <option value="كتيبة التدخل السريع 01">كتيبة التدخل السريع 01</option>
                                    <option value="كتيبة التدخل السريع 02">كتيبة التدخل السريع 02</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">الصفة المهنية</label>
                                <input type="text" id="profession" class="neumorphic-input" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">تاريخ الميلاد</label>
                                <input type="date" id="birthDate" class="neumorphic-input" required onchange="calculateAge()">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">السن</label>
                                <input type="number" id="age" class="neumorphic-input" readonly>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">الحالة العائلية</label>
                                <select id="maritalStatus" class="neumorphic-select" required>
                                    <option value="">اختر الحالة العائلية</option>
                                    <option value="متزوج (ة)">متزوج (ة)</option>
                                    <option value="أعزب (ة)">أعزب (ة)</option>
                                    <option value="مطلق (ة)">مطلق (ة)</option>
                                    <option value="أرمل (ة)">أرمل (ة)</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">عدد الأولاد</label>
                                <input type="number" id="children" class="neumorphic-input" min="0" value="0">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">سنة التقييم</label>
                                <select id="evaluationYear" class="neumorphic-select" required>
                                    <!-- السنوات ستضاف بواسطة JavaScript -->
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">النقطة</label>
                                <select id="score" class="neumorphic-select" required onchange="generateComment()">
                                    <option value="">اختر النقطة</option>
                                    <option value="4">4</option>
                                    <option value="5">5</option>
                                    <option value="6">6</option>
                                    <option value="7">7</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">الملاحظة</label>
                        <textarea id="comment" class="neumorphic-textarea" rows="4" readonly></textarea>
                    </div>

                    <!-- أزرار التحكم -->
                    <div class="form-actions">
                        <button type="submit" class="btn-action btn-save">
                            <i class="bi bi-save"></i>
                            حفظ البيانات
                        </button>
                        <button type="button" class="btn-action btn-edit" onclick="editEmployee()">
                            <i class="bi bi-pencil-square"></i>
                            تعديل
                        </button>
                        <button type="button" class="btn-action btn-clear" onclick="clearForm()">
                            <i class="bi bi-eraser"></i>
                            مسح الحقول
                        </button>
                        <button type="button" class="btn-action btn-delete" onclick="deleteEmployee()">
                            <i class="bi bi-trash"></i>
                            حذف
                        </button>
                        <button type="button" class="btn-action btn-close" onclick="closeInterface()">
                            <i class="bi bi-x-circle"></i>
                            إغلاق
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة الإحصائيات -->
    <div id="statisticsModal" class="modal-overlay" style="display: none;">
        <div class="modal-content neumorphic-card">
            <div class="modal-header">
                <h4>
                    <i class="bi bi-graph-up"></i>
                    إحصائيات الموظفين
                </h4>
                <button class="btn-close-modal" onclick="closeModal('statisticsModal')">
                    <i class="bi bi-x"></i>
                </button>
            </div>
            <div id="statisticsContent" class="modal-body">
                <!-- محتوى الإحصائيات -->
            </div>
        </div>
    </div>

    <!-- نافذة عرض البيانات -->
    <div id="dataViewModal" class="modal-overlay" style="display: none;">
        <div class="modal-content neumorphic-card modal-large">
            <div class="modal-header">
                <h4>
                    <i class="bi bi-table"></i>
                    عرض بيانات الموظفين
                </h4>
                <button class="btn-close-modal" onclick="closeModal('dataViewModal')">
                    <i class="bi bi-x"></i>
                </button>
            </div>
            <div id="dataViewContent" class="modal-body">
                <!-- جدول البيانات -->
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
