/* أزرار التحكم */
.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 2px solid rgba(108, 92, 231, 0.1);
}

.btn-action {
    background: var(--surface-color);
    border: none;
    border-radius: 15px;
    padding: 1rem 1.5rem;
    color: var(--text-primary);
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 
        8px 8px 16px rgba(0, 0, 0, 0.1),
        -8px -8px 16px rgba(255, 255, 255, 0.8);
    font-family: 'Cairo', sans-serif;
    min-width: 140px;
}

.btn-action:hover {
    transform: translateY(-3px);
    box-shadow: 
        12px 12px 20px rgba(0, 0, 0, 0.15),
        -12px -12px 20px rgba(255, 255, 255, 0.9);
}

.btn-action:active {
    transform: translateY(-1px);
    box-shadow: 
        6px 6px 12px rgba(0, 0, 0, 0.1),
        -6px -6px 12px rgba(255, 255, 255, 0.8);
}

.btn-action i {
    margin-left: 0.5rem;
}

.btn-save {
    background: linear-gradient(135deg, #00b894, #00cec9);
    color: white;
}

.btn-edit {
    background: linear-gradient(135deg, #fdcb6e, #e17055);
    color: white;
}

.btn-clear {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    color: white;
}

.btn-delete {
    background: linear-gradient(135deg, #fd79a8, #e84393);
    color: white;
}

.btn-close {
    background: linear-gradient(135deg, #636e72, #2d3436);
    color: white;
}

/* النوافذ المنبثقة */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.modal-content {
    max-width: 90%;
    max-height: 90%;
    overflow-y: auto;
    animation: slideIn 0.3s ease-out;
}

.modal-large {
    width: 95%;
    max-width: 1200px;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid rgba(108, 92, 231, 0.1);
}

.modal-header h4 {
    margin: 0;
    color: var(--text-primary);
    font-weight: 600;
}

.modal-header h4 i {
    margin-left: 0.5rem;
    color: var(--primary-color);
}

.btn-close-modal {
    background: var(--gradient-secondary);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    color: white;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 
        4px 4px 8px rgba(0, 0, 0, 0.2),
        -4px -4px 8px rgba(255, 255, 255, 0.8);
}

.btn-close-modal:hover {
    transform: scale(1.1);
}

.modal-body {
    max-height: 70vh;
    overflow-y: auto;
}

/* جدول البيانات */
.data-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
    background: var(--surface-color);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 
        8px 8px 16px rgba(0, 0, 0, 0.1),
        -8px -8px 16px rgba(255, 255, 255, 0.8);
}

.data-table th,
.data-table td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.data-table th {
    background: var(--gradient-primary);
    color: white;
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 10;
}

.data-table tr:hover {
    background: rgba(108, 92, 231, 0.05);
}

.data-table tr:nth-child(even) {
    background: rgba(248, 249, 250, 0.5);
}

/* بطاقات الإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--surface-color);
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 
        8px 8px 16px rgba(0, 0, 0, 0.1),
        -8px -8px 16px rgba(255, 255, 255, 0.8);
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 
        12px 12px 20px rgba(0, 0, 0, 0.15),
        -12px -12px 20px rgba(255, 255, 255, 0.9);
}

.stat-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--text-secondary);
    font-weight: 500;
}

/* مخطط النقاط */
.score-chart {
    background: var(--surface-color);
    border-radius: 15px;
    padding: 1.5rem;
    margin-top: 2rem;
    box-shadow: 
        8px 8px 16px rgba(0, 0, 0, 0.1),
        -8px -8px 16px rgba(255, 255, 255, 0.8);
}

.chart-title {
    text-align: center;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
    font-weight: 600;
}

.chart-bars {
    display: flex;
    align-items: end;
    justify-content: space-around;
    height: 200px;
    margin-bottom: 1rem;
}

.chart-bar {
    background: var(--gradient-primary);
    border-radius: 8px 8px 0 0;
    min-width: 60px;
    display: flex;
    align-items: end;
    justify-content: center;
    color: white;
    font-weight: 600;
    transition: var(--transition);
    position: relative;
}

.chart-bar:hover {
    transform: scale(1.05);
}

.chart-bar::after {
    content: attr(data-score);
    position: absolute;
    bottom: -30px;
    color: var(--text-primary);
    font-weight: 600;
}

/* رسائل التنبيه */
.alert {
    padding: 1rem 1.5rem;
    border-radius: 12px;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(135deg, #00b894, #00cec9);
    color: white;
}

.alert-error {
    background: linear-gradient(135deg, #fd79a8, #e84393);
    color: white;
}

.alert-info {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    color: white;
}

/* تصميم متجاوب للواجهة الرئيسية */
@media (max-width: 1024px) {
    .main-container {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        position: static;
    }
    
    .navbar-actions {
        flex-wrap: wrap;
        gap: 0.5rem;
    }
    
    .btn-nav {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 768px) {
    .form-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .btn-action {
        width: 100%;
        max-width: 300px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .modal-content {
        width: 95%;
        margin: 1rem;
    }
    
    .data-table {
        font-size: 0.9rem;
    }
    
    .data-table th,
    .data-table td {
        padding: 0.5rem;
    }
}
